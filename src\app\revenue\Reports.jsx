"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import SelectDropdown from "../../assignchannels/components/SelectDropdown";
import DatePicker from "react-datepicker";
import Table from "@/components/admin/Table";
import "react-datepicker/dist/react-datepicker.css";
import "../../../Main.scss";
import { formatDate, getCurrency, getLast7Days } from "@/utils/functions";
import ReactPaginate from "react-paginate";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { decodeToken } from "react-jwt";
function Reports() {
  const Token = localStorage.getItem("userPreferences");
  const [options, setOptions] = useState([]);
  const [customChannels, setcustomChannels] = useState([]);
  const [isOpen, setIsOpen] = useState([]);
  const [reportsTableData, setReportsTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [toggle, setToggle] = useState(false);
  const [totalRows, setTotalRows] = useState({});
  const [selectedOptions, setSelectedOptions] = useState({});
  const [selectedPartner, SetSelectedPartner] = useState("");
  const [showLoader, setshowLoader] = useState(false);
  const userData = Token && JSON.parse(window.atob(Token));
  const [filterData, setFilterData] = useState([]);
  const dropdownRefs = useRef([]);
  const [breakPoints, setBreakPoints] = useState({
    country: false,
    platform: false,
    date: false,
    customChannel: true,
  });

  const [customeChannelByUsers , setCustomeChannelByUsers] = useState([])

  const [selectLimit, setSelectLimit] = useState("25");
  const [search, setSearch] = useState("");
  const [dateRange, setDateRange] = useState([
    new Date(getLast7Days()[0]),
    new Date(getLast7Days()[getLast7Days()?.length - 1]),
  ]);
  const [partnerUsers, SetPartnerUsers] = useState([]);
  const [startDate, endDate] = dateRange;
  const fetchdata = useCallback(async () => {
    try {
      const response = await axios.get(`/api/admin/assignChnnals/get?ddl=true`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );
      if (response?.status === 200) {
        setcustomChannels(response?.data?.data);
        setOptions(
          response?.data?.data?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }))
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);
  const fetchdataUsers = useCallback(async () => {
    try {
      const response = await axios.get(`/api/adminusers/get?isddl=true&&userTypes=true`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );
      if (response?.status === 200) {
        SetPartnerUsers(
          response?.data?.data
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);
  useEffect(() => {
    fetchdata();
  }, [fetchdata]);

  const handleDateChange = (type, date) => {
    const newDate = new Date(date);
    if (type === "next") {
      newDate.setDate(newDate.getDate() + 1);
    } else if (type === "prev") {
      newDate.setDate(newDate.getDate() - 1);
    }
    setDateRange([newDate, newDate]);
  };

  useEffect(() => {
    if (["Admin", "Super Admin"]?.includes(userData?.IsAdmin)) {
      fetchdataUsers();
    }
  }, [fetchdataUsers, userData?.IsAdmin]);

  const renderRow = (report, idx) => {
    const checkCurrency = reportsTableData[0]?.headers
      ?.map((datacode, index) => {
        if (Object.keys(datacode)?.includes("currencyCode")) {
          return {
            index: index,
            currencyCode: datacode?.currencyCode,
          };
        }
      })
      ?.filter((dataNot) => dataNot !== undefined);
    const checkCustomChannelId = reportsTableData[0]?.headers?.findIndex(
      (headername) => headername?.name === "CUSTOM_CHANNEL_ID"
    );
    const checkmatricRatio = reportsTableData[0]?.headers
      ?.map((datacode, index) => {
        if (datacode?.type === "METRIC_RATIO") {
          return index;
        }
      })
      ?.filter((dataNot) => dataNot !== undefined);
    return (
      <tr key={idx}>
        {report?.cells?.map((datarows, index) => {
          const checkCurrencyIndex = checkCurrency?.find(
            (dataFind) => dataFind?.index === index
          );
          const checkMetRicRatio = checkmatricRatio?.find(
            (datashow) => datashow === index
          );
          return (
            <td
              key={index}
              className={`text-start ${
                idx === 0 && Object.keys(totalRows)?.length > 0 ? "bg-info" : ""
                }`}
            >
              {checkCurrencyIndex?.index === index &&
                getCurrency(checkCurrencyIndex?.currencyCode)}
              {checkCustomChannelId === index
                ? datarows?.value?.split(":")[1]
                : checkMetRicRatio === index
                  ? datarows?.value === "0"
                    ? "0"
                    : (+datarows?.value * 100).toFixed(2)
                  : datarows?.value}
              {checkMetRicRatio === index && "%"}
            </td>
          );
        })}
      </tr>
    );
  };
  const fetchChannelByUser = () => {
    const decodedData = decodeToken(userData?.Token)

    // For Super Admin, we'll fetch all channels to have them available
    if (["Super Admin", "Admin"].includes(userData?.IsAdmin)) {
      // Fetch all channels for Super Admin/Admin
      axios.get(`/api/admin/assignChnnals/get`, {
        headers: {
          Authorization: Token,
        },
      })
        .then((response) => {
          if (response?.status === 200 && response?.data?.data) {
            console.log(`Fetched ${response.data.data.length} total channels for Admin/Super Admin`);
            setCustomChannels(response?.data?.data || []);
          }
        })
        .catch((error) => {
          console.error("Error fetching all channels:", error?.response?.data?.error || "Internal server error");
        });

      // We don't set options here for Super Admin - that will be handled in fetchDataPatnaer
      // based on whether a partner is selected or not
      return;
    }

    // For Partner/Account users, use the getUserChannels API to get domain-filtered channels
    axios.get(`/api/admin/channels/getUserChannels?userId=${decodedData?.userId}`, {
      headers: {
        Authorization: Token,
      },
    })
      .then((response) => {
        console.log("getUserChannels API response:", response.data);

        // Check if we have filtered channels
        if (response?.data?.data && response?.data?.data.length > 0) {
          console.log(`Received ${response.data.data.length} filtered channels from API`);

          // Store the channels in state
          setCustomeChannelByUsers(response?.data?.data);

          // Set options for Partner/Account users
          const channelOptions = response?.data?.data?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }));

          console.log(`Setting ${channelOptions.length} options for dropdown`);
          setOptions(channelOptions);
        } else {
          console.log("No channels returned from API or empty array");
          setCustomeChannelByUsers([]);
          setOptions([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching user channels:", error?.response?.data?.error || "Internal server error");
      });
  }

  useEffect(() => {
    fetchChannelByUser();
  }, [fetchdata, userData?.IsAdmin]);

  const fetchDataPatnaer = useCallback(async () => {
    console.log("fetchDataPatnaer running with:", {
      selectedPartner,
      userType: userData?.IsAdmin,
      customChannelsCount: customChannels?.length,
      customeChannelByUsersCount: customeChannelByUsers?.length
    });

    try {
      // For Super Admin or Admin users
      if (["Super Admin", "Admin"].includes(userData?.IsAdmin)) {
        if (selectedPartner && selectedPartner !== "") {
          // If a partner is selected, get their specific channels based on domain prefixes
          try {
            console.log(`Partner selected: ${selectedPartner}, fetching domain-filtered channels`);

            // Get channels filtered by domain prefixes for this user
            const channelsResponse = await axios.get(
              `/api/admin/channels/getUserChannels?userId=${selectedPartner}`,
              {
                headers: {
                  Authorization: Token,
                },
              }
            );

            // Check if we have filtered channels
            if (channelsResponse?.status === 200 && channelsResponse?.data?.data && channelsResponse?.data?.data.length > 0) {
              const filteredChannels = channelsResponse?.data?.data;

              const channelOptions = filteredChannels.map((channel) => ({
                label: channel?.displayName,
                value: channel?._id,
              }));

              setOptions(channelOptions);
            } else {
              // No channels match the domain prefixes, show empty dropdown
              setOptions([]);
            }
          } catch (error) {
            console.error("Error fetching partner channels:", error);
            setOptions([]);
          }
        } else {
          // No partner selected (or "Select" option chosen), show all channels
          console.log("No partner selected, using all channels for Admin/Super Admin:", customChannels?.length);

          const allOptions = customChannels?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }));

          console.log("Setting all options for dropdown:", allOptions?.length);
          setOptions(allOptions);
        }
      }
      // For Partner and Account users
      else if (["Partner", "Account"].includes(userData?.IsAdmin)) {
        // For Partner and Account users, use the channels from our getUserChannels API
        // These are already filtered by domain prefixes
        console.log("Using domain-filtered channels for Partner/Account:", customeChannelByUsers?.length);

        // Use the channels returned from our API
        // The API now correctly filters channels based on domain prefixes
        // or returns all channels as a fallback if no matches are found
        const filteredOptions = customeChannelByUsers?.map((channels) => ({
          label: channels?.displayName,
          value: channels?._id,
        }));

        console.log("Setting options for dropdown:", filteredOptions?.length);
        setOptions(filteredOptions);
      }
    } catch (error) {
      console.error("Error in fetchDataPatnaer:", error?.response?.data?.error || "Internal server error");
    }
  }, [customChannels, selectedPartner, customeChannelByUsers, userData?.IsAdmin, Token]);

  useEffect(() => {
    fetchDataPatnaer();
  }, [fetchDataPatnaer]);

  const handleBreckPoints = (e) => {
    const { name, checked } = e?.target;
    setBreakPoints((prev) => ({ ...prev, [name]: checked }));
  };
  const combineTotals = (array, key = "totals") => {
    // console.log('combine totals',array,key);
    return array.reduce((acc, val) => {
      acc[key] ??= { cells: Array(val[key]?.cells?.length || 0).fill({}) };
      val[key]?.cells?.forEach((cell, index) => {
        if (cell?.value) {
          const currentValue = acc[key].cells[index]?.value || "0";
          acc[key].cells[index] = {
            value: (
              parseFloat(currentValue) + parseFloat(cell.value)
            ).toString(),
          };
        }
      });

      return acc;
    }, {});
  };
  const finalHeaders = (headersList) => {
    const findChannalName = headersList?.find(
      (dataHead) =>
        dataHead?.name === "CUSTOM_CHANNEL_NAME" &&
        dataHead?.type === "DIMENSION"
    );
    if (findChannalName) {
      const finalData = headersList?.map((headername, index) => {
        return {
          name: headername?.name?.toLowerCase()?.replaceAll("_", " "),
          class: "text-start text-capitalize",
          key: index,
        };
      });
      return finalData;
    } else {
      const findIndex = headersList?.findIndex(
        (dHead) =>
          dHead?.name === "CUSTOM_CHANNEL_ID" && dHead?.type === "DIMENSION"
      );
      headersList?.splice(findIndex + 1, 0, {
        name: "CUSTOM_CHANNEL_NAME",
        type: "DIMENSION",
      });
      const finalData = headersList?.map((headername, index) => {
        return {
          name: headername?.name?.toLowerCase()?.replaceAll("_", " "),
          class: "text-start text-capitalize",
          key: index,
        };
      });
      return finalData;
    }
  };
  const searchApi = async () => {
    // Get selected channel options or empty array if none selected
    const selectedChannelOptions = selectedOptions["userIdCustomReport"]?.map(
      (dataReport) => dataReport?.value
    ) || [];

    // For Partner and Account users, if no channels are selected, use all their domain-based channels
    let channelsToUse = selectedChannelOptions;
    if (["Partner", "Account"].includes(userData?.IsAdmin) && selectedChannelOptions.length === 0) {
      // If no channels selected, use all available channels for this user
      channelsToUse = customeChannelByUsers.map(channel => channel._id);
    }

    const obj = {
      breakPoints: JSON.stringify(breakPoints),
      selectedOptions: JSON.stringify(channelsToUse),
      selectedPartner,
      toggle,
    };

    const params = new URLSearchParams(
      Object.entries(obj).reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {})
    ).toString();

    try {
      setshowLoader(true);
      const response = await axios.get(
        `/api/auth?savedgenerate=true&startDate=${formatDate(
          startDate
        )}&endDate=${formatDate(endDate)}&${params}`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );

      if (response?.status === 200) {
        setshowLoader(false);

        var a = Array.isArray(response?.data?.data)
        ? finalTotals(
          combineTotals(response?.data?.data)?.totals,
          response?.data?.data[0]?.headers
        )
        : finalTotals(
          response?.data?.data?.totals,
          response?.data?.data?.headers
        ) || {};

        setTotalRows(a);
        const combinedRows = Array.isArray(response?.data?.data)
          ? response?.data?.data
          : [response?.data?.data];

        setReportsTableData(combinedRows);
      }
    } catch (error) {
      setshowLoader(false);
      console.error(error?.response?.data?.error || "Internal server error");
    }
  };

  function calculateMetrics(data) {
    const { headers, rows } = data;

    // Initialize sums for relevant metrics
    let estimatedEarningsSum = 0;
    let impressionsSum = 0;
    let clicksSum = 0;

    // Calculate sums for ESTIMATED_EARNINGS, IMPRESSIONS, and CLICKS
    rows.forEach(row => {
        row.cells.forEach((cell, index) => {
            const header = headers[index];
            const value = parseFloat(cell.value) || 0;

            if (header.name === "ESTIMATED_EARNINGS") {
                estimatedEarningsSum += value;
            } else if (header.name === "IMPRESSIONS") {
                impressionsSum += value;
            } else if (header.name === "CLICKS") {
                clicksSum += value;
            }
        });
    });

    // Calculate derived metrics
    const impressionsRPM = (estimatedEarningsSum / impressionsSum) * 1000; // RPM formula
    const impressionsCTR = (clicksSum / impressionsSum); // CTR formula
    const costPerClick = estimatedEarningsSum / clicksSum; // CPC formula

    // Prepare the response in the desired format
    const response = {
        cells: headers.map(header => {
            if (header.name === "ESTIMATED_EARNINGS") {
                return { value: estimatedEarningsSum.toFixed(2) };
            } else if (header.name === "IMPRESSIONS") {
                return { value: impressionsSum.toFixed(0) };
            } else if (header.name === "IMPRESSIONS_RPM") {
                return { value: impressionsRPM.toFixed(2) };
            } else if (header.name === "CLICKS") {
                return { value: clicksSum.toFixed(0) };
            } else if (header.name === "IMPRESSIONS_CTR") {
                return { value: impressionsCTR.toFixed(4) };
            } else if (header.name === "COST_PER_CLICK") {
                return { value: costPerClick.toFixed(2) };
            } else {
                return {}; // Empty object for non-metric columns
            }
        })
    };

    return response;
}
  const combinedFun = () => {
    const combined = [];
    reportsTableData?.forEach((dataMap) => {
      if (Array.isArray(dataMap?.rows) && dataMap?.rows?.length > 0) {
        combined.push(...dataMap.rows);
      }
    });
    const findChannelIdIndex = reportsTableData[0]?.headers?.findIndex(
      (header) =>
        header?.name === "CUSTOM_CHANNEL_ID" && header?.type === "DIMENSION"
    );
    const findChannelnameIndex = reportsTableData[0]?.headers?.findIndex(
      (header) =>
        header?.name === "CUSTOM_CHANNEL_NAME" && header?.type === "DIMENSION"
    );
    const updatedArray = combined.map((row) => {
      const channelId = row?.cells?.[findChannelIdIndex]?.value;
      const checkvalue = findChannelnameIndex !== -1 ? row?.cells?.[findChannelnameIndex]?.value :null;
      const channelName =
        customChannels?.find(
          (channel) => channel?.reportingDimensionId === channelId
        )?.displayName || "No any Custom Channels";
      if (!checkvalue && findChannelnameIndex === -1) {
        row?.cells?.splice(findChannelIdIndex + 1, 0, { value: channelName });
        return row;
      } else if (!checkvalue && findChannelnameIndex) {
        row?.cells?.splice(findChannelIdIndex, 0, { value: channelName });
        return row;
      } else {
        return row;
      }
    });
    return updatedArray;
  };


  const finalCombinedData = combinedFun();
  const optionsData = [25, 50, 100, 250, 500];
  const handlePageClick = (page) => {
    const newOffset =
      (page.selected * +selectLimit) %
      (filterData?.length > 0 ? filterData?.length : finalCombinedData?.length);
    setPage(newOffset);
  };
  const endOffset = page + +selectLimit;
  const finalItems = filterData?.length > 0 ? filterData : finalCombinedData;
  const currentItems = finalItems?.slice(page, endOffset);
  const pageCount = Math.ceil(
    (filterData?.length > 0 ? filterData?.length : finalCombinedData?.length) /
    +selectLimit
  );
  useEffect(() => {
    const filterData = finalCombinedData?.filter((datamap) =>
      datamap?.cells?.some((valu) =>
        valu?.value?.toLowerCase()?.includes(search?.toLowerCase())
      )
    );
    setFilterData(filterData);
    setPage(filterData?.length > 0 ? 0 : page);
  }, [search]);
  function handledates(range) {
    const today = new Date();
    let startDate, endDate;

    switch (range) {
      case "today":
        startDate = new Date(today);
        endDate = new Date(today);
        break;

      case "yesterday":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(startDate);
        break;

      case "thismonth":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate =
          today?.getDate() >
            new Date(today.getFullYear(), today.getMonth() + 1, 0)?.getDate()
            ? new Date(today.getFullYear(), today.getMonth() + 1, 0)
            : today;
        break;

      case "lastmonth":
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;

      case "last30days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        break;

      case "last7days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        endDate = new Date(today);
        break;

      default:
        startDate = null;
        endDate = null;
        break;
    }
    if (startDate && endDate) {
      setDateRange([startDate, endDate]);
    }
  }
  useEffect(() => {
    searchApi();
  }, []);
  const finalTotals = (totals, headers) => {
    // console.log('final totals',totals,headers)
    const findChannalName = headers?.find(
      (dataHead) =>
        dataHead?.name === "CUSTOM_CHANNEL_NAME" &&
        dataHead?.type === "DIMENSION"
    );
    const findIndex = headers?.findIndex(
      (dHead) =>
        dHead?.name === "CUSTOM_CHANNEL_ID" && dHead?.type === "DIMENSION"
    );
    if (findChannalName) {
      return totals;
    } else {
      const copyCombined = JSON.parse(JSON.stringify(totals?.cells));
      copyCombined?.splice(findIndex + 1, 0, {});
      return { cells: copyCombined };
    }
  };

  useEffect(() => {
    if (!dropdownRefs?.current[0]) {
      dropdownRefs.current[0] = React.createRef();
    }
    const handleClickOutside = (event) => {
      dropdownRefs?.current?.forEach((ref, index) => {
        if (ref && !ref?.current?.contains(event.target)) {
          setIsOpen((prevState) => {
            const updatedState = [...prevState];
            updatedState[index] = false;
            return updatedState;
          });
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <>
      <div className="col-12">
        <div className="card card-shadow">
          <div className="card-body">
            <div className="row align-items-center justify-content-between">
              <h2 className="card-title width_fit">Revenue</h2>
              <div className="row col-12 justify-content-between">
                <div className="col-12 col-md-12 col-lg-6 col-xl-2 mb-2">
                  <h2 className="dropdownColor pb-1 font-size-14">
                    Breakpoints :
                  </h2>
                  <div className="row user-select-none gap-1 align-items-center">
                    <div className="d-flex user-select-none gap-1 align-items-center">
                      <input
                        type="checkbox"
                        name="country"
                        checked={breakPoints?.country}
                        id="country"
                        onChange={(e) => handleBreckPoints(e)}
                      />
                      <label htmlFor="country" className="font-size-14">
                        Country
                      </label>
                    </div>
                    <div className="d-flex user-select-none gap-1 align-items-center">
                      <input
                        type="checkbox"
                        checked={breakPoints?.platform}
                        name="platform"
                        id="platform"
                        onChange={(e) => handleBreckPoints(e)}
                      />
                      <label htmlFor="platform" className="font-size-14">
                        Platform
                      </label>
                    </div>
                    <div className="d-flex user-select-none gap-1 align-items-center">
                      <input
                        type="checkbox"
                        checked={breakPoints?.date}
                        name="date"
                        id="date"
                        onChange={(e) => handleBreckPoints(e)}
                      />
                      <label htmlFor="date" className="font-size-14">
                        Date
                      </label>
                    </div>
                    <div className="d-flex user-select-none gap-1 align-items-center">
                      <input
                        type="checkbox"
                        checked={breakPoints?.customChannel}
                        name="customChannel"
                        id="customChannel"
                        onChange={(e) => handleBreckPoints(e)}
                      />
                      <label htmlFor="customChannel" className="font-size-14">
                        Custom channel
                      </label>
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-12 col-lg-6 col-xl-2 mb-2">
                  <h2 className="dropdownColor pb-1 font-size-14">
                    Currency :
                  </h2>
                  <div className="d-flex align-items-center width_fit justify-content-center">
                    <label className="switch">
                      <input type="checkbox" checked={toggle} name="currency" />
                      <span
                        onClick={() => setToggle(!toggle)}
                        className="slider round"
                      ></span>
                    </label>
                    <span className="text-white font-size-14 position-absolute">
                      {toggle ? "INR" : "USD"}
                    </span>
                  </div>
                </div>
                {["Admin", "Super Admin"]?.includes(userData?.IsAdmin) && (
                  <div className="col-12 col-md-12 col-lg-6 col-xl-2 mb-2">
                    <h2 className="dropdownColor pb-1 font-size-14">
                      Partners :
                    </h2>
                    <select
                      className="form-select col-12 font-size-14"
                      onChange={(e) => {
                        SetSelectedPartner(e?.target?.value);
                      }}
                    >
                      <option selected={selectedPartner === ""} value={""}>
                        Select
                      </option>
                      {partnerUsers?.map((dataUser, index) => {
                        return (
                          <option
                            key={index + 1}
                            selected={dataUser?._id === selectedPartner}
                            value={dataUser?._id}
                          >
                            {dataUser?.name}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                )}
                <div className="col-12 col-md-12 col-lg-6 col-xl-2 mb-2">
                  <h2 className="dropdownColor pb-1 font-size-14">
                    Custom Channels :
                  </h2>
                  <SelectDropdown
                    options={options}
                    dropdowName={"Select Channels"}
                    selectedOptions={selectedOptions}
                    setSelectedOptions={setSelectedOptions}
                    index={0}
                    userId={"userIdCustomReport"}
                    setIsOpen={setIsOpen}
                    isOpen={isOpen[0]}
                    dropdownRef={dropdownRefs?.current[0]}
                  />
                </div>
                <div className="col-12 col-md-12 col-lg-6 col-xl-4 mb-2">
                  <h2 className="dropdownColor pb-1 font-size-14">Date :</h2>
                  <div className="row justify-content-between align-items-center">
                    <div className="col-10 col-md-9 d-flex align-items-center col-xl-8 mb-2">
                      <div className="col-2 d-flex justify-content-center align-items-center">
                        <button
                          type="button"
                          className="btn-reportIcon"
                          onClick={() => {
                            handleDateChange("prev", startDate);
                          }}
                        >
                          <FaChevronLeft size={16} color="gray" />
                        </button>
                      </div>
                      <div className="col-8">
                        <DatePicker
                          selectsRange={true}
                          startDate={startDate}
                          endDate={endDate}
                          dateFormat={"dd-MM-YYYY"}
                          className="form-control font-size-14"
                          onChange={(update) => {
                            setDateRange(update);
                          }}
                        >
                          <div className="row col-12 gap-1 justify-content-start align-items-center">
                            <button
                              type="button"
                              onClick={() => {
                                handledates("today");
                              }}
                              className="width_fit link-primary border-0 bg-transparent"
                            >
                              Today
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                handledates("yesterday");
                              }}
                              className="width_fit link-primary border-0 bg-transparent"
                            >
                              Yesterday
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                handledates("last7days");
                              }}
                              className="width_fit link-primary border-0 bg-transparent"
                            >
                              Last 7 Days
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                handledates("last30days");
                              }}
                              className="width_fit link-primary border-0 bg-transparent"
                            >
                              Last 30 Days
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                handledates("thismonth");
                              }}
                              className="width_fit link-primary border-0 bg-transparent"
                            >
                              This Month
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                handledates("lastmonth");
                              }}
                              className="width_fit link-primary border-0 bg-transparent"
                            >
                              Last Month
                            </button>
                          </div>
                        </DatePicker>
                      </div>
                      <div className="col-2 d-flex justify-content-center align-items-center">
                        <button
                          type="button"
                          className="btn-reportIcon"
                          onClick={() => {
                            handleDateChange("next", endDate);
                          }}
                        >
                          <FaChevronRight size={16} color="gray" />
                        </button>
                      </div>
                    </div>
                    <div className="col-6 col-sm-2 col-md-3 col-xl-4 mb-2">
                      <button
                        type="button"
                        onClick={() => {
                          searchApi();
                        }}
                        className="btn btn-primary col-12 font-size-14"
                      >
                        Search
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="card card-shadow">
          <div className="card-body pt-4">
            {showLoader && (
              <div className="row align-items-center justify-content-center w-100 h-25">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            )}
            {reportsTableData?.length > 0 && showLoader === false && (
              <>
                <div className="row gap-2 justify-content-between align-items-center gap-md-0 mb-2">
                  <div className="d-flex gap-1 width_fit align-items-center">
                    <select
                      className="form-select width_fit"
                      onChange={(e) => {
                        setSelectLimit(e?.target?.value);
                      }}
                    >
                      {optionsData?.map((dataOptions) => {
                        return (
                          <option
                            selected={selectLimit === dataOptions}
                            value={dataOptions}
                            key={dataOptions}
                          >
                            {dataOptions}
                          </option>
                        );
                      })}
                    </select>
                    <label className="width_fit whitespacenowarp font-size-14">
                      entries per page
                    </label>
                  </div>
                  <div className="d-flex gap-2 align-items-center col-3">
                    <label className="width_fit whitespacenowarp font-size-14">
                      Search :{" "}
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      id="search"
                      name="search"
                      value={search}
                      onChange={(e) => {
                        setSearch(e?.target?.value);
                      }}
                    />
                  </div>
                </div>
                <Table
                  renderRow={renderRow}
                  showLoader={showLoader}
                  filterDatamessage={filterData?.length === 0 && search}
                  data={currentItems}
                  columns={finalHeaders(reportsTableData[0]?.headers)}
                  sortbycol={true}
                  totalRows={totalRows}
                  noDatamsg={"No any reports"}
                  setSelectLimit={setSelectLimit}
                  selectLimit={selectLimit}
                  setSearch={setSearch}
                  search={search}
                  showSearch={false}
                />
                {currentItems?.length > 0 && (
                  <div
                    className={`${
                      filterData?.length === 0 && search
                        ? "d-none"
                        : "row justify-content-between align-items-center gap-2 gap-md-0 mt-4"
                      }`}
                  >
                    <p className="width_fit m-0 font-size-14">
                      Showing {page + 1} to{" "}
                      {(filterData?.length > 0
                        ? filterData?.length
                        : finalCombinedData?.length) < endOffset
                        ? filterData?.length > 0
                          ? filterData?.length
                          : finalCombinedData?.length
                        : endOffset}{" "}
                      of{" "}
                      {filterData?.length > 0
                        ? filterData?.length
                        : finalCombinedData?.length}{" "}
                      entries
                    </p>
                    <ReactPaginate
                      breakLabel="..."
                      nextLabel={<FaChevronRight size={12} />}
                      onPageChange={handlePageClick}
                      pageRangeDisplayed={1}
                      marginPagesDisplayed={2}
                      pageCount={pageCount}
                      containerClassName={
                        "pagination justify-content-center flex-wrap width_fit m-0"
                      }
                      pageClassName={"page-item"}
                      pageLinkClassName={"page-link"}
                      breakClassName={"page-item"}
                      breakLinkClassName={"page-link"}
                      previousClassName={"page-item"}
                      previousLinkClassName={"page-link"}
                      nextClassName={"page-item"}
                      nextLinkClassName={"page-link"}
                      activeClassName={"active"}
                      previousLabel={<FaChevronLeft size={12} />}
                      renderOnZeroPageCount={null}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default Reports;
