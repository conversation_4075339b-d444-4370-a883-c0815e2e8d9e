"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { formatDate, getCurrency, getLast7Days } from "@/utils/functions";
import ReactPaginate from "react-paginate";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { decodeToken } from "react-jwt";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";

import { Button } from "@/components/ui-elements/button";
import { cn } from "@/lib/utils";

// Custom Multi-Select Component
function MultiSelectDropdown({ options, selectedOptions, setSelectedOptions, userId, placeholder = "Select options" }) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const handleOptionToggle = (option) => {
    const currentSelected = selectedOptions[userId] || [];
    const isSelected = currentSelected.some(item => item.value === option.value);

    let newSelected;
    if (isSelected) {
      newSelected = currentSelected.filter(item => item.value !== option.value);
    } else {
      newSelected = [...currentSelected, option];
    }

    setSelectedOptions(prev => ({
      ...prev,
      [userId]: newSelected
    }));
  };

  const selectedItems = selectedOptions[userId] || [];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white text-left flex justify-between items-center"
      >
        <span className="truncate">
          {selectedItems.length > 0
            ? `${selectedItems.length} selected`
            : placeholder
          }
        </span>
        <FaChevronLeft className={cn("transform transition-transform", isOpen ? "rotate-90" : "-rotate-90")} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {options.map((option) => {
            const isSelected = selectedItems.some(item => item.value === option.value);
            return (
              <div
                key={option.value}
                onClick={() => handleOptionToggle(option)}
                className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-2 cursor-pointer flex items-center gap-2"
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => {}}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span className="text-dark dark:text-white">{option.label}</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

function Reports() {
  const Token = localStorage.getItem("userPreferences");
  const [options, setOptions] = useState([]);
  const [customChannels, setcustomChannels] = useState([]);

  const [reportsTableData, setReportsTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [toggle, setToggle] = useState(false);
  const [totalRows, setTotalRows] = useState({});
  const [selectedOptions, setSelectedOptions] = useState({});
  const [selectedPartner, SetSelectedPartner] = useState("");
  const [showLoader, setshowLoader] = useState(false);
  const userData = Token && JSON.parse(window.atob(Token));
  const [filterData, setFilterData] = useState([]);
  const [breakPoints, setBreakPoints] = useState({
    country: false,
    platform: false,
    date: false,
    customChannel: true,
  });

  const [customeChannelByUsers , setCustomeChannelByUsers] = useState([])

  const [selectLimit, setSelectLimit] = useState("25");
  const [search, setSearch] = useState("");
  const [dateRange, setDateRange] = useState([
    new Date(getLast7Days()[0]),
    new Date(getLast7Days()[getLast7Days()?.length - 1]),
  ]);
  const [partnerUsers, SetPartnerUsers] = useState([]);
  const [startDate, endDate] = dateRange;
  const fetchdata = useCallback(async () => {
    try {
      const response = await axios.get(`/api/admin/assignChnnals/get?ddl=true`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );
      if (response?.status === 200) {
        setcustomChannels(response?.data?.data);
        setOptions(
          response?.data?.data?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }))
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);
  const fetchdataUsers = useCallback(async () => {
    try {
      const response = await axios.get(`/api/adminusers/get?isddl=true&&userTypes=true`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );
      if (response?.status === 200) {
        SetPartnerUsers(
          response?.data?.data
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);
  useEffect(() => {
    fetchdata();
  }, [fetchdata]);

  const handleDateChange = (type, date) => {
    const newDate = new Date(date);
    if (type === "next") {
      newDate.setDate(newDate.getDate() + 1);
    } else if (type === "prev") {
      newDate.setDate(newDate.getDate() - 1);
    }
    setDateRange([newDate, newDate]);
  };

  useEffect(() => {
    if (["Admin", "Super Admin"]?.includes(userData?.IsAdmin)) {
      fetchdataUsers();
    }
  }, [fetchdataUsers, userData?.IsAdmin]);

  const renderRow = (report, idx) => {
    const checkCurrency = reportsTableData[0]?.headers
      ?.map((datacode, index) => {
        if (Object.keys(datacode)?.includes("currencyCode")) {
          return {
            index: index,
            currencyCode: datacode?.currencyCode,
          };
        }
      })
      ?.filter((dataNot) => dataNot !== undefined);
    const checkCustomChannelId = reportsTableData[0]?.headers?.findIndex(
      (headername) => headername?.name === "CUSTOM_CHANNEL_ID"
    );
    const checkmatricRatio = reportsTableData[0]?.headers
      ?.map((datacode, index) => {
        if (datacode?.type === "METRIC_RATIO") {
          return index;
        }
      })
      ?.filter((dataNot) => dataNot !== undefined);
    return (
      <tr key={idx} className="hover:bg-gray-50 dark:hover:bg-dark-2 transition-colors">
        {report?.cells?.map((datarows, index) => {
          const checkCurrencyIndex = checkCurrency?.find(
            (dataFind) => dataFind?.index === index
          );
          const checkMetRicRatio = checkmatricRatio?.find(
            (datashow) => datashow === index
          );
          return (
            <td
              key={index}
              className="px-4 py-3 text-sm text-dark dark:text-white border-b border-stroke dark:border-dark-3"
            >
              {checkCurrencyIndex?.index === index &&
                getCurrency(checkCurrencyIndex?.currencyCode)}
              {checkCustomChannelId === index
                ? datarows?.value?.split(":")[1]
                : checkMetRicRatio === index
                  ? datarows?.value === "0"
                    ? "0"
                    : (+datarows?.value * 100).toFixed(2)
                  : datarows?.value}
              {checkMetRicRatio === index && "%"}
            </td>
          );
        })}
      </tr>
    );
  };
  const fetchChannelByUser = () => {
    const decodedData = decodeToken(userData?.Token)

    // For Super Admin, we'll fetch all channels to have them available
    if (["Super Admin", "Admin"].includes(userData?.IsAdmin)) {
      // Fetch all channels for Super Admin/Admin
      axios.get(`/api/admin/assignChnnals/get`, {
        headers: {
          Authorization: Token,
        },
      })
        .then((response) => {
          if (response?.status === 200 && response?.data?.data) {
            console.log(`Fetched ${response.data.data.length} total channels for Admin/Super Admin`);
            setcustomChannels(response?.data?.data || []);
          }
        })
        .catch((error) => {
          console.error("Error fetching all channels:", error?.response?.data?.error || "Internal server error");
        });

      // We don't set options here for Super Admin - that will be handled in fetchDataPatnaer
      // based on whether a partner is selected or not
      return;
    }

    // For Partner/Account users, use the getUserChannels API to get domain-filtered channels
    axios.get(`/api/admin/channels/getUserChannels?userId=${decodedData?.userId}`, {
      headers: {
        Authorization: Token,
      },
    })
      .then((response) => {
        console.log("getUserChannels API response:", response.data);

        // Check if we have filtered channels
        if (response?.data?.data && response?.data?.data.length > 0) {
          console.log(`Received ${response.data.data.length} filtered channels from API`);

          // Store the channels in state
          setCustomeChannelByUsers(response?.data?.data);

          // Set options for Partner/Account users
          const channelOptions = response?.data?.data?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }));

          console.log(`Setting ${channelOptions.length} options for dropdown`);
          setOptions(channelOptions);
        } else {
          console.log("No channels returned from API or empty array");
          setCustomeChannelByUsers([]);
          setOptions([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching user channels:", error?.response?.data?.error || "Internal server error");
      });
  }

  useEffect(() => {
    fetchChannelByUser();
  }, [fetchdata, userData?.IsAdmin]);

  const fetchDataPatnaer = useCallback(async () => {
    console.log("fetchDataPatnaer running with:", {
      selectedPartner,
      userType: userData?.IsAdmin,
      customChannelsCount: customChannels?.length,
      customeChannelByUsersCount: customeChannelByUsers?.length
    });

    try {
      // For Super Admin or Admin users
      if (["Super Admin", "Admin"].includes(userData?.IsAdmin)) {
        if (selectedPartner && selectedPartner !== "") {
          // If a partner is selected, get their specific channels based on domain prefixes
          try {
            console.log(`Partner selected: ${selectedPartner}, fetching domain-filtered channels`);

            // Get channels filtered by domain prefixes for this user
            const channelsResponse = await axios.get(
              `/api/admin/channels/getUserChannels?userId=${selectedPartner}`,
              {
                headers: {
                  Authorization: Token,
                },
              }
            );

            // Check if we have filtered channels
            if (channelsResponse?.status === 200 && channelsResponse?.data?.data && channelsResponse?.data?.data.length > 0) {
              const filteredChannels = channelsResponse?.data?.data;

              const channelOptions = filteredChannels.map((channel) => ({
                label: channel?.displayName,
                value: channel?._id,
              }));

              setOptions(channelOptions);
            } else {
              // No channels match the domain prefixes, show empty dropdown
              setOptions([]);
            }
          } catch (error) {
            console.error("Error fetching partner channels:", error);
            setOptions([]);
          }
        } else {
          // No partner selected (or "Select" option chosen), show all channels
          console.log("No partner selected, using all channels for Admin/Super Admin:", customChannels?.length);

          const allOptions = customChannels?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }));

          console.log("Setting all options for dropdown:", allOptions?.length);
          setOptions(allOptions);
        }
      }
      // For Partner and Account users
      else if (["Partner", "Account"].includes(userData?.IsAdmin)) {
        // For Partner and Account users, use the channels from our getUserChannels API
        // These are already filtered by domain prefixes
        console.log("Using domain-filtered channels for Partner/Account:", customeChannelByUsers?.length);

        // Use the channels returned from our API
        // The API now correctly filters channels based on domain prefixes
        // or returns all channels as a fallback if no matches are found
        const filteredOptions = customeChannelByUsers?.map((channels) => ({
          label: channels?.displayName,
          value: channels?._id,
        }));

        console.log("Setting options for dropdown:", filteredOptions?.length);
        setOptions(filteredOptions);
      }
    } catch (error) {
      console.error("Error in fetchDataPatnaer:", error?.response?.data?.error || "Internal server error");
    }
  }, [customChannels, selectedPartner, customeChannelByUsers, userData?.IsAdmin, Token]);

  useEffect(() => {
    fetchDataPatnaer();
  }, [fetchDataPatnaer]);

  const handleBreckPoints = (e) => {
    const { name, checked } = e?.target;
    setBreakPoints((prev) => ({ ...prev, [name]: checked }));
  };
  const combineTotals = (array, key = "totals") => {
    // console.log('combine totals',array,key);
    return array.reduce((acc, val) => {
      acc[key] ??= { cells: Array(val[key]?.cells?.length || 0).fill({}) };
      val[key]?.cells?.forEach((cell, index) => {
        if (cell?.value) {
          const currentValue = acc[key].cells[index]?.value || "0";
          acc[key].cells[index] = {
            value: (
              parseFloat(currentValue) + parseFloat(cell.value)
            ).toString(),
          };
        }
      });

      return acc;
    }, {});
  };
  const finalHeaders = (headersList) => {
    const findChannalName = headersList?.find(
      (dataHead) =>
        dataHead?.name === "CUSTOM_CHANNEL_NAME" &&
        dataHead?.type === "DIMENSION"
    );
    if (findChannalName) {
      const finalData = headersList?.map((headername, index) => {
        return {
          name: headername?.name?.toLowerCase()?.replaceAll("_", " "),
          class: "text-start text-capitalize",
          key: index,
        };
      });
      return finalData;
    } else {
      const findIndex = headersList?.findIndex(
        (dHead) =>
          dHead?.name === "CUSTOM_CHANNEL_ID" && dHead?.type === "DIMENSION"
      );
      headersList?.splice(findIndex + 1, 0, {
        name: "CUSTOM_CHANNEL_NAME",
        type: "DIMENSION",
      });
      const finalData = headersList?.map((headername, index) => {
        return {
          name: headername?.name?.toLowerCase()?.replaceAll("_", " "),
          class: "text-start text-capitalize",
          key: index,
        };
      });
      return finalData;
    }
  };
  const searchApi = async () => {
    // Get selected channel options or empty array if none selected
    const selectedChannelOptions = selectedOptions["userIdCustomReport"]?.map(
      (dataReport) => dataReport?.value
    ) || [];

    // For Partner and Account users, if no channels are selected, use all their domain-based channels
    let channelsToUse = selectedChannelOptions;
    if (["Partner", "Account"].includes(userData?.IsAdmin) && selectedChannelOptions.length === 0) {
      // If no channels selected, use all available channels for this user
      channelsToUse = customeChannelByUsers.map(channel => channel._id);
    }

    const obj = {
      breakPoints: JSON.stringify(breakPoints),
      selectedOptions: JSON.stringify(channelsToUse),
      selectedPartner,
      toggle,
    };

    const params = new URLSearchParams(
      Object.entries(obj).reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {})
    ).toString();

    try {
      setshowLoader(true);
      const response = await axios.get(
        `/api/auth?savedgenerate=true&startDate=${formatDate(
          startDate
        )}&endDate=${formatDate(endDate)}&${params}`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );

      if (response?.status === 200) {
        setshowLoader(false);

        var a = Array.isArray(response?.data?.data)
        ? finalTotals(
          combineTotals(response?.data?.data)?.totals,
          response?.data?.data[0]?.headers
        )
        : finalTotals(
          response?.data?.data?.totals,
          response?.data?.data?.headers
        ) || {};

        setTotalRows(a);
        const combinedRows = Array.isArray(response?.data?.data)
          ? response?.data?.data
          : [response?.data?.data];

        setReportsTableData(combinedRows);
      }
    } catch (error) {
      setshowLoader(false);
      console.error(error?.response?.data?.error || "Internal server error");
    }
  };


  const combinedFun = () => {
    const combined = [];
    reportsTableData?.forEach((dataMap) => {
      if (Array.isArray(dataMap?.rows) && dataMap?.rows?.length > 0) {
        combined.push(...dataMap.rows);
      }
    });
    const findChannelIdIndex = reportsTableData[0]?.headers?.findIndex(
      (header) =>
        header?.name === "CUSTOM_CHANNEL_ID" && header?.type === "DIMENSION"
    );
    const findChannelnameIndex = reportsTableData[0]?.headers?.findIndex(
      (header) =>
        header?.name === "CUSTOM_CHANNEL_NAME" && header?.type === "DIMENSION"
    );
    const updatedArray = combined.map((row) => {
      const channelId = row?.cells?.[findChannelIdIndex]?.value;
      const checkvalue = findChannelnameIndex !== -1 ? row?.cells?.[findChannelnameIndex]?.value :null;
      const channelName =
        customChannels?.find(
          (channel) => channel?.reportingDimensionId === channelId
        )?.displayName || "No any Custom Channels";
      if (!checkvalue && findChannelnameIndex === -1) {
        row?.cells?.splice(findChannelIdIndex + 1, 0, { value: channelName });
        return row;
      } else if (!checkvalue && findChannelnameIndex) {
        row?.cells?.splice(findChannelIdIndex, 0, { value: channelName });
        return row;
      } else {
        return row;
      }
    });
    return updatedArray;
  };


  const finalCombinedData = combinedFun();
  const optionsData = [25, 50, 100, 250, 500];
  const handlePageClick = (page) => {
    const newOffset =
      (page.selected * +selectLimit) %
      (filterData?.length > 0 ? filterData?.length : finalCombinedData?.length);
    setPage(newOffset);
  };
  const endOffset = page + +selectLimit;
  const finalItems = filterData?.length > 0 ? filterData : finalCombinedData;
  const currentItems = finalItems?.slice(page, endOffset);
  const pageCount = Math.ceil(
    (filterData?.length > 0 ? filterData?.length : finalCombinedData?.length) /
    +selectLimit
  );
  useEffect(() => {
    const filterData = finalCombinedData?.filter((datamap) =>
      datamap?.cells?.some((valu) =>
        valu?.value?.toLowerCase()?.includes(search?.toLowerCase())
      )
    );
    setFilterData(filterData);
    setPage(filterData?.length > 0 ? 0 : page);
  }, [search]);
  function handledates(range) {
    const today = new Date();
    let startDate, endDate;

    switch (range) {
      case "today":
        startDate = new Date(today);
        endDate = new Date(today);
        break;

      case "yesterday":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(startDate);
        break;

      case "thismonth":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate =
          today?.getDate() >
            new Date(today.getFullYear(), today.getMonth() + 1, 0)?.getDate()
            ? new Date(today.getFullYear(), today.getMonth() + 1, 0)
            : today;
        break;

      case "lastmonth":
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;

      case "last30days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        break;

      case "last7days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        endDate = new Date(today);
        break;

      default:
        startDate = null;
        endDate = null;
        break;
    }
    if (startDate && endDate) {
      setDateRange([startDate, endDate]);
    }
  }
  useEffect(() => {
    searchApi();
  }, []);
  const finalTotals = (totals, headers) => {
    // console.log('final totals',totals,headers)
    const findChannalName = headers?.find(
      (dataHead) =>
        dataHead?.name === "CUSTOM_CHANNEL_NAME" &&
        dataHead?.type === "DIMENSION"
    );
    const findIndex = headers?.findIndex(
      (dHead) =>
        dHead?.name === "CUSTOM_CHANNEL_ID" && dHead?.type === "DIMENSION"
    );
    if (findChannalName) {
      return totals;
    } else {
      const copyCombined = JSON.parse(JSON.stringify(totals?.cells));
      copyCombined?.splice(findIndex + 1, 0, {});
      return { cells: copyCombined };
    }
  };


  return (
    <>
      <Breadcrumb pageName="Revenue" />

      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="mb-6">
          <h2 className="text-[26px] font-bold leading-[30px] text-dark dark:text-white mb-6">Revenue Reports</h2>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-5 mb-6">
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">
                Breakpoints:
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    name="country"
                    checked={breakPoints?.country}
                    id="country"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="country" className="text-sm text-dark dark:text-white">
                    Country
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={breakPoints?.platform}
                    name="platform"
                    id="platform"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="platform" className="text-sm text-dark dark:text-white">
                    Platform
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={breakPoints?.date}
                    name="date"
                    id="date"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="date" className="text-sm text-dark dark:text-white">
                    Date
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={breakPoints?.customChannel}
                    name="customChannel"
                    id="customChannel"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="customChannel" className="text-sm text-dark dark:text-white">
                    Custom channel
                  </label>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">
                Currency:
              </h3>
              <div className="flex items-center gap-3">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={toggle}
                    name="currency"
                    onChange={() => setToggle(!toggle)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                </label>
                <span className="text-sm font-medium text-dark dark:text-white">
                  {toggle ? "INR" : "USD"}
                </span>
              </div>
            </div>

            {["Admin", "Super Admin"]?.includes(userData?.IsAdmin) && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-dark dark:text-white">
                  Partners:
                </h3>
                <select
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
                  onChange={(e) => {
                    SetSelectedPartner(e?.target?.value);
                  }}
                  value={selectedPartner}
                >
                  <option value="">Select Partner</option>
                  {partnerUsers?.map((dataUser, index) => {
                    return (
                      <option
                        key={index + 1}
                        value={dataUser?._id}
                      >
                        {dataUser?.name}
                      </option>
                    );
                  })}
                </select>
              </div>
            )}

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">
                Custom Channels:
              </h3>
              <MultiSelectDropdown
                options={options}
                placeholder="Select Channels"
                selectedOptions={selectedOptions}
                setSelectedOptions={setSelectedOptions}
                userId="userIdCustomReport"
              />
            </div>

            <div className="xl:col-span-2 space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">Date Range:</h3>
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => handleDateChange("prev", startDate)}
                  className="p-2 rounded-lg border border-stroke hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2"
                >
                  <FaChevronLeft size={16} className="text-gray-500" />
                </button>

                <div className="flex-1">
                  <DatePicker
                    selectsRange={true}
                    startDate={startDate}
                    endDate={endDate}
                    dateFormat="dd-MM-yyyy"
                    className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
                    onChange={(update) => setDateRange(update)}
                  >
                    <div className="grid grid-cols-2 gap-2 p-4">
                      <button
                        type="button"
                        onClick={() => handledates("today")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Today
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("yesterday")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Yesterday
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("last7days")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Last 7 Days
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("last30days")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Last 30 Days
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("thismonth")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        This Month
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("lastmonth")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Last Month
                      </button>
                    </div>
                  </DatePicker>
                </div>

                <button
                  type="button"
                  onClick={() => handleDateChange("next", endDate)}
                  className="p-2 rounded-lg border border-stroke hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2"
                >
                  <FaChevronRight size={16} className="text-gray-500" />
                </button>
              </div>

              <Button
                label="Search"
                variant="primary"
                onClick={searchApi}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card mt-6">
          {showLoader && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-dark dark:text-white">Loading...</span>
            </div>
          )}

          {reportsTableData?.length > 0 && showLoader === false && (
            <>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <select
                    className="rounded-lg border border-stroke bg-transparent px-3 py-2 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
                    onChange={(e) => setSelectLimit(e?.target?.value)}
                    value={selectLimit}
                  >
                    {optionsData?.map((dataOptions) => {
                      return (
                        <option
                          value={dataOptions}
                          key={dataOptions}
                        >
                          {dataOptions}
                        </option>
                      );
                    })}
                  </select>
                  <label className="text-sm text-dark dark:text-white whitespace-nowrap">
                    entries per page
                  </label>
                </div>

                <div className="flex items-center gap-2">
                  <label className="text-sm text-dark dark:text-white whitespace-nowrap">
                    Search:
                  </label>
                  <input
                    type="text"
                    className="rounded-lg border border-stroke bg-transparent px-3 py-2 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
                    id="search"
                    name="search"
                    value={search}
                    onChange={(e) => setSearch(e?.target?.value)}
                    placeholder="Search..."
                  />
                </div>
              </div>

              {/* Custom Table */}
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead>
                    <tr className="bg-gray-2 dark:bg-dark-2">
                      {finalHeaders(reportsTableData[0]?.headers)?.map((column, index) => (
                        <th
                          key={index}
                          className="px-4 py-3 text-left text-sm font-medium text-dark dark:text-white border-b border-stroke dark:border-dark-3"
                        >
                          {column.name}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {/* Total Row */}
                    {Object.keys(totalRows)?.length > 0 && (
                      <tr className="bg-blue-50 dark:bg-blue-900/20">
                        {totalRows?.cells?.map((cell, index) => (
                          <td
                            key={index}
                            className="px-4 py-3 text-sm font-semibold text-dark dark:text-white border-b border-stroke dark:border-dark-3"
                          >
                            {cell?.value || ''}
                          </td>
                        ))}
                      </tr>
                    )}

                    {/* Data Rows */}
                    {currentItems?.length > 0 ? (
                      currentItems.map((item, index) => renderRow(item, index))
                    ) : (
                      <tr>
                        <td
                          colSpan={finalHeaders(reportsTableData[0]?.headers)?.length}
                          className="px-4 py-8 text-center text-sm text-gray-500 dark:text-gray-400"
                        >
                          {filterData?.length === 0 && search ? "No matching records found" : "No reports available"}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {currentItems?.length > 0 && !(filterData?.length === 0 && search) && (
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
                  <p className="text-sm text-dark dark:text-white">
                    Showing {page + 1} to{" "}
                    {(filterData?.length > 0
                      ? filterData?.length
                      : finalCombinedData?.length) < endOffset
                      ? filterData?.length > 0
                        ? filterData?.length
                        : finalCombinedData?.length
                      : endOffset}{" "}
                    of{" "}
                    {filterData?.length > 0
                      ? filterData?.length
                      : finalCombinedData?.length}{" "}
                    entries
                  </p>

                  <ReactPaginate
                    breakLabel="..."
                    nextLabel={<FaChevronRight size={12} />}
                    onPageChange={handlePageClick}
                    pageRangeDisplayed={1}
                    marginPagesDisplayed={2}
                    pageCount={pageCount}
                    containerClassName="flex items-center gap-1"
                    pageClassName="inline-block"
                    pageLinkClassName="px-3 py-2 text-sm border border-stroke rounded hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2 text-dark dark:text-white transition-colors"
                    breakClassName="inline-block"
                    breakLinkClassName="px-3 py-2 text-sm text-dark dark:text-white"
                    previousClassName="inline-block"
                    previousLinkClassName="px-3 py-2 text-sm border border-stroke rounded hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2 text-dark dark:text-white transition-colors"
                    nextClassName="inline-block"
                    nextLinkClassName="px-3 py-2 text-sm border border-stroke rounded hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2 text-dark dark:text-white transition-colors"
                    activeClassName="bg-primary text-white border-primary"
                    previousLabel={<FaChevronLeft size={12} />}
                    renderOnZeroPageCount={null}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </>
    );
  }

export default Reports;
