"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { formatDate, getCurrency, getLast7Days } from "@/utils/functions";
import ReactPaginate from "react-paginate";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { decodeToken } from "react-jwt";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";

import { Button } from "@/components/ui-elements/button";
import { cn } from "@/lib/utils";
import CustomDataTable from "@/components/DataTable/CustomDataTable";


function Reports() {
  const Token = localStorage.getItem("userPreferences");
  const [options, setOptions] = useState([]);
  const [customChannels, setcustomChannels] = useState([]);

  const [reportsTableData, setReportsTableData] = useState([]);
  const [toggle, setToggle] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [selectedPartner, SetSelectedPartner] = useState("");
  const [showLoader, setshowLoader] = useState(false);
  const userData = Token && JSON.parse(window.atob(Token));
  const [breakPoints, setBreakPoints] = useState({
    country: false,
    platform: false,
    date: false,
    customChannel: true,
  });

  const [customeChannelByUsers , setCustomeChannelByUsers] = useState([])



  // CustomDataTable specific states
  const [tableData, setTableData] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);
  const [tablePage, setTablePage] = useState(0);
  const [tableRowsPerPage, setTableRowsPerPage] = useState(25);
  const [tableOrder, setTableOrder] = useState("asc");
  const [tableOrderBy, setTableOrderBy] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState([
    new Date(getLast7Days()[0]),
    new Date(getLast7Days()[getLast7Days()?.length - 1]),
  ]);
  const [partnerUsers, SetPartnerUsers] = useState([]);
  const [startDate, endDate] = dateRange;
  const fetchdata = useCallback(async () => {
    try {
      const response = await axios.get(`/api/admin/assignChnnals/get?ddl=true`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );
      if (response?.status === 200) {
        setcustomChannels(response?.data?.data);
        setOptions(
          response?.data?.data?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }))
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);
  const fetchdataUsers = useCallback(async () => {
    try {
      const response = await axios.get(`/api/adminusers/get?isddl=true&&userTypes=true`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );
      if (response?.status === 200) {
        SetPartnerUsers(
          response?.data?.data
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);
  useEffect(() => {
    fetchdata();
  }, [fetchdata]);

  const handleDateChange = (type, date) => {
    const newDate = new Date(date);
    if (type === "next") {
      newDate.setDate(newDate.getDate() + 1);
    } else if (type === "prev") {
      newDate.setDate(newDate.getDate() - 1);
    }
    setDateRange([newDate, newDate]);
  };

  useEffect(() => {
    if (["Admin", "Super Admin"]?.includes(userData?.IsAdmin)) {
      fetchdataUsers();
    }
  }, [fetchdataUsers, userData?.IsAdmin]);


  const fetchChannelByUser = () => {
    const decodedData = decodeToken(userData?.Token)

    // For Super Admin, we'll fetch all channels to have them available
    if (["Super Admin", "Admin"].includes(userData?.IsAdmin)) {
      // Fetch all channels for Super Admin/Admin
      axios.get(`/api/admin/assignChnnals/get`, {
        headers: {
          Authorization: Token,
        },
      })
        .then((response) => {
          if (response?.status === 200 && response?.data?.data) {
            console.log(`Fetched ${response.data.data.length} total channels for Admin/Super Admin`);
            setcustomChannels(response?.data?.data || []);
          }
        })
        .catch((error) => {
          console.error("Error fetching all channels:", error?.response?.data?.error || "Internal server error");
        });

      // We don't set options here for Super Admin - that will be handled in fetchDataPatnaer
      // based on whether a partner is selected or not
      return;
    }

    // For Partner/Account users, use the getUserChannels API to get domain-filtered channels
    axios.get(`/api/admin/channels/getUserChannels?userId=${decodedData?.userId}`, {
      headers: {
        Authorization: Token,
      },
    })
      .then((response) => {
        console.log("getUserChannels API response:", response.data);

        // Check if we have filtered channels
        if (response?.data?.data && response?.data?.data.length > 0) {
          console.log(`Received ${response.data.data.length} filtered channels from API`);

          // Store the channels in state
          setCustomeChannelByUsers(response?.data?.data);

          // Set options for Partner/Account users
          const channelOptions = response?.data?.data?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }));

          console.log(`Setting ${channelOptions.length} options for dropdown`);
          setOptions(channelOptions);
        } else {
          console.log("No channels returned from API or empty array");
          setCustomeChannelByUsers([]);
          setOptions([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching user channels:", error?.response?.data?.error || "Internal server error");
      });
  }

  useEffect(() => {
    fetchChannelByUser();
  }, [fetchdata, userData?.IsAdmin]);

  const fetchDataPatnaer = useCallback(async () => {
    console.log("fetchDataPatnaer running with:", {
      selectedPartner,
      userType: userData?.IsAdmin,
      customChannelsCount: customChannels?.length,
      customeChannelByUsersCount: customeChannelByUsers?.length
    });

    try {
      // For Super Admin or Admin users
      if (["Super Admin", "Admin"].includes(userData?.IsAdmin)) {
        if (selectedPartner && selectedPartner !== "") {
          // If a partner is selected, get their specific channels based on domain prefixes
          try {
            console.log(`Partner selected: ${selectedPartner}, fetching domain-filtered channels`);

            // Get channels filtered by domain prefixes for this user
            const channelsResponse = await axios.get(
              `/api/admin/channels/getUserChannels?userId=${selectedPartner}`,
              {
                headers: {
                  Authorization: Token,
                },
              }
            );

            // Check if we have filtered channels
            if (channelsResponse?.status === 200 && channelsResponse?.data?.data && channelsResponse?.data?.data.length > 0) {
              const filteredChannels = channelsResponse?.data?.data;

              const channelOptions = filteredChannels.map((channel) => ({
                label: channel?.displayName,
                value: channel?._id,
              }));

              setOptions(channelOptions);
            } else {
              // No channels match the domain prefixes, show empty dropdown
              setOptions([]);
            }
          } catch (error) {
            console.error("Error fetching partner channels:", error);
            setOptions([]);
          }
        } else {
          // No partner selected (or "Select" option chosen), show all channels
          console.log("No partner selected, using all channels for Admin/Super Admin:", customChannels?.length);

          const allOptions = customChannels?.map((channels) => ({
            label: channels?.displayName,
            value: channels?._id,
          }));

          console.log("Setting all options for dropdown:", allOptions?.length);
          setOptions(allOptions);
        }
      }
      // For Partner and Account users
      else if (["Partner", "Account"].includes(userData?.IsAdmin)) {
        // For Partner and Account users, use the channels from our getUserChannels API
        // These are already filtered by domain prefixes
        console.log("Using domain-filtered channels for Partner/Account:", customeChannelByUsers?.length);

        // Use the channels returned from our API
        // The API now correctly filters channels based on domain prefixes
        // or returns all channels as a fallback if no matches are found
        const filteredOptions = customeChannelByUsers?.map((channels) => ({
          label: channels?.displayName,
          value: channels?._id,
        }));

        console.log("Setting options for dropdown:", filteredOptions?.length);
        setOptions(filteredOptions);
      }
    } catch (error) {
      console.error("Error in fetchDataPatnaer:", error?.response?.data?.error || "Internal server error");
    }
  }, [customChannels, selectedPartner, customeChannelByUsers, userData?.IsAdmin, Token]);

  useEffect(() => {
    fetchDataPatnaer();
  }, [fetchDataPatnaer]);

  const handleBreckPoints = (e) => {
    const { name, checked } = e?.target;
    setBreakPoints((prev) => ({ ...prev, [name]: checked }));
  };
  const combineTotals = (array, key = "totals") => {
    // console.log('combine totals',array,key);
    return array.reduce((acc, val) => {
      acc[key] ??= { cells: Array(val[key]?.cells?.length || 0).fill({}) };
      val[key]?.cells?.forEach((cell, index) => {
        if (cell?.value) {
          const currentValue = acc[key].cells[index]?.value || "0";
          acc[key].cells[index] = {
            value: (
              parseFloat(currentValue) + parseFloat(cell.value)
            ).toString(),
          };
        }
      });

      return acc;
    }, {});
  };

  const searchApi = async () => {
    // Get selected channel options or empty array if none selected
    const selectedChannelOptions = selectedOptions["userIdCustomReport"]?.map(
      (dataReport) => dataReport?.value
    ) || [];

    // For Partner and Account users, if no channels are selected, use all their domain-based channels
    let channelsToUse = selectedChannelOptions;
    if (["Partner", "Account"].includes(userData?.IsAdmin) && selectedChannelOptions.length === 0) {
      // If no channels selected, use all available channels for this user
      channelsToUse = customeChannelByUsers.map(channel => channel._id);
    }

    const obj = {
      breakPoints: JSON.stringify(breakPoints),
      selectedOptions: JSON.stringify(channelsToUse),
      selectedPartner,
      toggle,
    };

    const params = new URLSearchParams(
      Object.entries(obj).reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {})
    ).toString();

    try {
      setshowLoader(true);
      const response = await axios.get(
        `/api/auth?savedgenerate=true&startDate=${formatDate(
          startDate
        )}&endDate=${formatDate(endDate)}&${params}`,
        {
          headers: {
            Authorization: Token,
          },
        }
      );

      if (response?.status === 200) {
        setshowLoader(false);

        var a = Array.isArray(response?.data?.data)
        ? finalTotals(
          combineTotals(response?.data?.data)?.totals,
          response?.data?.data[0]?.headers
        )
        : finalTotals(
          response?.data?.data?.totals,
          response?.data?.data?.headers
        ) || {};

        setTotalRows(a);
        const combinedRows = Array.isArray(response?.data?.data)
          ? response?.data?.data
          : [response?.data?.data];

        setReportsTableData(combinedRows);
      }
    } catch (error) {
      setshowLoader(false);
      console.error(error?.response?.data?.error || "Internal server error");
    }
  };


  const combinedFun = () => {
    const combined = [];
    reportsTableData?.forEach((dataMap) => {
      if (Array.isArray(dataMap?.rows) && dataMap?.rows?.length > 0) {
        combined.push(...dataMap.rows);
      }
    });
    const findChannelIdIndex = reportsTableData[0]?.headers?.findIndex(
      (header) =>
        header?.name === "CUSTOM_CHANNEL_ID" && header?.type === "DIMENSION"
    );
    const findChannelnameIndex = reportsTableData[0]?.headers?.findIndex(
      (header) =>
        header?.name === "CUSTOM_CHANNEL_NAME" && header?.type === "DIMENSION"
    );
    const updatedArray = combined.map((row) => {
      const channelId = row?.cells?.[findChannelIdIndex]?.value;
      const checkvalue = findChannelnameIndex !== -1 ? row?.cells?.[findChannelnameIndex]?.value :null;
      const channelName =
        customChannels?.find(
          (channel) => channel?.reportingDimensionId === channelId
        )?.displayName || "No any Custom Channels";
      if (!checkvalue && findChannelnameIndex === -1) {
        row?.cells?.splice(findChannelIdIndex + 1, 0, { value: channelName });
        return row;
      } else if (!checkvalue && findChannelnameIndex) {
        row?.cells?.splice(findChannelIdIndex, 0, { value: channelName });
        return row;
      } else {
        return row;
      }
    });
    return updatedArray;
  };



  function handledates(range) {
    const today = new Date();
    let startDate, endDate;

    switch (range) {
      case "today":
        startDate = new Date(today);
        endDate = new Date(today);
        break;

      case "yesterday":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(startDate);
        break;

      case "thismonth":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate =
          today?.getDate() >
            new Date(today.getFullYear(), today.getMonth() + 1, 0)?.getDate()
            ? new Date(today.getFullYear(), today.getMonth() + 1, 0)
            : today;
        break;

      case "lastmonth":
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;

      case "last30days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        break;

      case "last7days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        endDate = new Date(today);
        break;

      default:
        startDate = null;
        endDate = null;
        break;
    }
    if (startDate && endDate) {
      setDateRange([startDate, endDate]);
    }
  }
  useEffect(() => {
    searchApi();
  }, []);
  const finalTotals = (totals, headers) => {
    // console.log('final totals',totals,headers)
    const findChannalName = headers?.find(
      (dataHead) =>
        dataHead?.name === "CUSTOM_CHANNEL_NAME" &&
        dataHead?.type === "DIMENSION"
    );
    const findIndex = headers?.findIndex(
      (dHead) =>
        dHead?.name === "CUSTOM_CHANNEL_ID" && dHead?.type === "DIMENSION"
    );
    if (findChannalName) {
      return totals;
    } else {
      const copyCombined = JSON.parse(JSON.stringify(totals?.cells));
      copyCombined?.splice(findIndex + 1, 0, {});
      return { cells: copyCombined };
    }
  };

  // Transform data for CustomDataTable
  const transformDataForTable = useCallback(() => {
    if (!reportsTableData?.length) return;

    // Create columns from headers
    const headers = reportsTableData[0]?.headers || [];
    const columns = headers.map((header, index) => ({
      id: `col_${index}`,
      label: header?.name?.toLowerCase()?.replaceAll("_", " ") || `Column ${index + 1}`
    }));

    setTableColumns(columns);

    // Transform rows data
    const combinedData = combinedFun();
    const transformedRows = combinedData.map((row, index) => {
      const rowData = { Id: index };

      row.cells?.forEach((cell, cellIndex) => {
        let value = cell?.value || '';

        // Apply the same transformations as in renderRow
        const checkCurrency = headers
          ?.map((datacode, idx) => {
            if (Object.keys(datacode)?.includes("currencyCode")) {
              return {
                index: idx,
                currencyCode: datacode?.currencyCode,
              };
            }
          })
          ?.filter((dataNot) => dataNot !== undefined);

        const checkCustomChannelId = headers?.findIndex(
          (headername) => headername?.name === "CUSTOM_CHANNEL_ID"
        );

        const checkmatricRatio = headers
          ?.map((datacode, idx) => {
            if (datacode?.type === "METRIC_RATIO") {
              return idx;
            }
          })
          ?.filter((dataNot) => dataNot !== undefined);

        const checkCurrencyIndex = checkCurrency?.find(
          (dataFind) => dataFind?.index === cellIndex
        );
        const checkMetRicRatio = checkmatricRatio?.find(
          (datashow) => datashow === cellIndex
        );

        // Apply transformations
        if (checkCurrencyIndex?.index === cellIndex) {
          value = getCurrency(checkCurrencyIndex?.currencyCode) + value;
        } else if (checkCustomChannelId === cellIndex) {
          value = value?.split(":")[1] || value;
        } else if (checkMetRicRatio === cellIndex) {
          value = value === "0" ? "0%" : (+value * 100).toFixed(2) + "%";
        }

        rowData[`col_${cellIndex}`] = value;
      });

      return rowData;
    });

    setTableData(transformedRows);
  }, [reportsTableData, customChannels]);

  // CustomDataTable event handlers
  const handleTablePageChange = (newPage) => {
    setTablePage(newPage);
  };

  const handleTableRowsPerPageChange = (newRowsPerPage) => {
    setTableRowsPerPage(newRowsPerPage);
    setTablePage(0);
  };

  const handleTableRequestSort = (_, property) => {
    const isAsc = tableOrderBy === property && tableOrder === "asc";
    setTableOrder(isAsc ? "desc" : "asc");
    setTableOrderBy(property);
  };

  const handleTableSearchChange = (term) => {
    setSearchTerm(term);
    setTablePage(0);
  };

  useEffect(() => {
    transformDataForTable();
  }, [transformDataForTable]);

  useEffect(() => {
    searchApi();
  }, []);

  return (
    <>
      <Breadcrumb pageName="Revenue" />

      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="mb-6">
          <h2 className="text-[26px] font-bold leading-[30px] text-dark dark:text-white mb-6">Revenue Reports</h2>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-5 mb-6">
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">
                Breakpoints:
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    name="country"
                    checked={breakPoints?.country}
                    id="country"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="country" className="text-sm text-dark dark:text-white">
                    Country
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={breakPoints?.platform}
                    name="platform"
                    id="platform"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="platform" className="text-sm text-dark dark:text-white">
                    Platform
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={breakPoints?.date}
                    name="date"
                    id="date"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="date" className="text-sm text-dark dark:text-white">
                    Date
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={breakPoints?.customChannel}
                    name="customChannel"
                    id="customChannel"
                    onChange={(e) => handleBreckPoints(e)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="customChannel" className="text-sm text-dark dark:text-white">
                    Custom channel
                  </label>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">
                Currency:
              </h3>
              <div className="flex items-center gap-3">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={toggle}
                    name="currency"
                    onChange={() => setToggle(!toggle)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                </label>
                <span className="text-sm font-medium text-dark dark:text-white">
                  {toggle ? "INR" : "USD"}
                </span>
              </div>
            </div>

            {["Admin", "Super Admin"]?.includes(userData?.IsAdmin) && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-dark dark:text-white">
                  Partners:
                </h3>
                <select
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
                  onChange={(e) => {
                    SetSelectedPartner(e?.target?.value);
                  }}
                  value={selectedPartner}
                >
                  <option value="">Select Partner</option>
                  {partnerUsers?.map((dataUser, index) => {
                    return (
                      <option
                        key={index + 1}
                        value={dataUser?._id}
                      >
                        {dataUser?.name}
                      </option>
                    );
                  })}
                </select>
              </div>
            )}

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">
                Custom Channels:
              </h3>
              <MultiSelectDropdown
                options={options}
                placeholder="Select Channels"
                selectedOptions={selectedOptions}
                setSelectedOptions={setSelectedOptions}
                userId="userIdCustomReport"
              />
            </div>

            <div className="xl:col-span-2 space-y-3">
              <h3 className="text-sm font-medium text-dark dark:text-white">Date Range:</h3>
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => handleDateChange("prev", startDate)}
                  className="p-2 rounded-lg border border-stroke hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2"
                >
                  <FaChevronLeft size={16} className="text-gray-500" />
                </button>

                <div className="flex-1">
                  <DatePicker
                    selectsRange={true}
                    startDate={startDate}
                    endDate={endDate}
                    dateFormat="dd-MM-yyyy"
                    className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
                    onChange={(update) => setDateRange(update)}
                  >
                    <div className="grid grid-cols-2 gap-2 p-4">
                      <button
                        type="button"
                        onClick={() => handledates("today")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Today
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("yesterday")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Yesterday
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("last7days")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Last 7 Days
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("last30days")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Last 30 Days
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("thismonth")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        This Month
                      </button>
                      <button
                        type="button"
                        onClick={() => handledates("lastmonth")}
                        className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded"
                      >
                        Last Month
                      </button>
                    </div>
                  </DatePicker>
                </div>

                <button
                  type="button"
                  onClick={() => handleDateChange("next", endDate)}
                  className="p-2 rounded-lg border border-stroke hover:bg-gray-100 dark:border-dark-3 dark:hover:bg-dark-2"
                >
                  <FaChevronRight size={16} className="text-gray-500" />
                </button>
              </div>

              <Button
                label="Search"
                variant="primary"
                onClick={searchApi}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Table Section */}
        {reportsTableData?.length > 0 && (
          <div className="mt-6">
            <CustomDataTable
              isLoading={showLoader}
              columns={tableColumns}
              rows={tableData}
              searchTerm={searchTerm}
              onSearchChange={handleTableSearchChange}
              page={tablePage}
              rowsPerPage={tableRowsPerPage}
              onPageChange={handleTablePageChange}
              onRowsPerPageChange={handleTableRowsPerPageChange}
              totalCount={tableData?.length || 0}
              order={tableOrder}
              orderBy={tableOrderBy}
              onRequestSort={handleTableRequestSort}
              isAction={true} // Hide actions column for reports
            />
          </div>
        )}
      </>
    );
}

export default Reports;
