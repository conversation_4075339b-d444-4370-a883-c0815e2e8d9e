import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            User_Type: string;
            Id: string;
        };
        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        let where: any = {
            IsDeleted: false
        };

        if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin') {
            where.CreatedBy = user.Id;
        }
        const domainWithUsers = await prisma.domain.findMany({
            where,
            orderBy: {
                CreatedAt: 'desc'
            },
            select: {
                Id: true,
                Name: true,
            }
        });

        const transformedData = domainWithUsers.map(domain => ({
            ...domain,
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
