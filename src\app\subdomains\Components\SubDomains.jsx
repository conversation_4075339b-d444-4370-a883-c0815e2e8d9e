"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import useDebounce from "@/hooks/useDebounce";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import Swal from "sweetalert2";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { FaPlus } from "react-icons/fa";
import { CloseIcon } from "@/assets/icons";

const SubDomainModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEdit,
  users,
  accountDetails,
  selectedDomain,
}) => {
  const [formData, setFormData] = useState({
    Name: "",
    Url: "",
    Domain: "",
    AssignUsers: [],
    AccountId: [],
    CId: "",
    HeadTag: "",
    HeadTagScript: "",
    HeadTagScriptLandingPage: "",
    HeadTagScriptSearchPage: "",
    GId: "",
    AWId: "",
    SendTo: "",
  });

  useEffect(() => {
    if (isEdit && initialData) {
      setFormData({
        Name: initialData.Name || "",
        Url: initialData.Url || "",
        Domain: selectedDomain?.Id || "",
        AssignUsers: initialData.UserMappings?.map((mapping) => mapping) || [],
        AccountId: initialData.AccountId?.map((mapping) => mapping) || [],
        CId: initialData.CId || "",
        HeadTag: initialData.HeadTag || "",
        HeadTagScript: initialData.HeadTagScript || "",
        HeadTagScriptLandingPage: initialData.HeadTagScriptLandingPage || "",
        HeadTagScriptSearchPage: initialData.HeadTagScriptSearchPage || "",
        GId: initialData.GId || "",
        AWId: initialData.AWId || "",
        SendTo: initialData.SendTo || "",
        Id: initialData.Id || "",
      });
    } else {
      setFormData({
        Name: "",
        Url: "",
        Domain: "",
        AssignUsers: [],
        AccountId: [],
        CId: "",
        HeadTag: "",
        HeadTagScript: "",
        HeadTagScriptLandingPage: "",
        HeadTagScriptSearchPage: "",
        GId: "",
        AWId: "",
        SendTo: "",
      });
    }
  }, [initialData, isEdit, selectedDomain]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleCancel = () => {
    onClose();
    setFormData({
      Name: "",
      Url: "",
      Domain: "",
      AssignUsers: [],
      AccountId: [],
      CId: "",
      HeadTag: "",
      HeadTag: "",
      HeadTagScript: "",
      HeadTagScriptLandingPage: "",
      HeadTagScriptSearchPage: "",
      GId: "",
      AWId: "",
      SendTo: "",
    });
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={handleCancel}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {isEdit ? "Edit Subdomain" : "Add Subdomain"}
        </span>
        <IconButton
          aria-label="close"
          onClick={handleCancel}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form
          onSubmit={handleSubmit}
          style={{ display: "flex", flexDirection: "column", gap: 16 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 20,
            }}
          >
            <InputGroup
              label="Subdomain Name"
              type="text"
              name="Name"
              value={formData.Name}
              handleChange={handleChange}
              placeholder="Enter subdomain name"
              required
            />

            <InputGroup
              label="URL"
              type="text"
              name="Url"
              value={formData.Url}
              handleChange={handleChange}
              placeholder="Enter URL"
              required
            />

            <MultiSelectDropdown
              label="Assign Users"
              options={users}
              placeholder="Select users"
              value={formData.AssignUsers}
              onChange={(selectedIds) =>
                setFormData({ ...formData, AssignUsers: selectedIds })
              }
              displayKey="Name"
              idKey="Id"
              showSelectAll
            />

            <MultiSelectDropdown
              label="Account Details"
              options={accountDetails}
              placeholder="Select accounts"
              value={formData.AccountId}
              onChange={(selectedIds) =>
                setFormData({ ...formData, AccountId: selectedIds })
              }
              displayKey="DescriptiveName"
              idKey="AccountId"
              showSelectAll
            />

            <InputGroup
              label="CID (Client ID)"
              type="text"
              name="CId"
              value={formData.CId}
              handleChange={handleChange}
              placeholder="Enter CID"
            />

            <InputGroup
              label="Send To"
              name="SendTo"
              type="text"
              placeholder="Enter Send To"
              value={formData.SendTo}
              handleChange={handleChange}
            />

            <InputGroup
              label="AwId"
              name="AWId"
              type="text"
              placeholder="Enter AWId"
              value={formData.AWId}
              handleChange={handleChange}
            />

            <InputGroup
              label="GId"
              name="GId"
              type="text"
              placeholder="Enter GId"
              value={formData.GId}
              handleChange={handleChange}
            />

            <TextAreaGroup
              label="Head Tag"
              name="HeadTag"
              value={formData.HeadTag}
              handleChange={handleChange}
              rows={3}
            />

            <TextAreaGroup
              label="Head Tag Script"
              name="HeadTagScript"
              value={formData.HeadTagScript}
              handleChange={handleChange}
              rows={3}
            />

            <TextAreaGroup
              label="Head Tag Script Landing Page"
              name="HeadTagScriptLandingPage"
              value={formData.HeadTagScriptLandingPage}
              handleChange={handleChange}
              rows={3}
            />

            <TextAreaGroup
              label="Head Tag Script Search & Search Result Page"
              name="HeadTagScriptSearchPage"
              value={formData.HeadTagScriptSearchPage}
              handleChange={handleChange}
              rows={3}
            />
          </div>
        </form>
      </DialogContent>

      <DialogActions>
        <Button
          type="submit"
          label={isEdit ? "Update SubDomain" : "Add SubDomain"}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
        />
      </DialogActions>
    </Dialog>
  );
};

const SubDomains = () => {
  const [domainList, setDomainList] = useState([]);
  const [domainListLoader, setDomainListLoader] = useState(false);
  const [selectedOption, setSelectedOption] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [modalSearchTerm, setModalSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [modalPage, setModalPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [modalRowsPerPage, setModalRowsPerPage] = useState(10);
  const [order, setOrder] = useState("desc");
  const [modalOrder, setModalOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("");
  const [modalOrderBy, setModalOrderBy] = useState("");
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [modalState, setModalState] = useState({
    show: false,
    isEdit: false,
    data: null,
  });
  const [users, setUsers] = useState([]);
  const [accountDetails, setAccountDetails] = useState([]);
  const [forceRefresh, setForceRefresh] = useState(0);
  const [usersTableModal, setUsersTableModal] = useState({
    show: false,
    subdomainId: null,
    subdomainName: "",
    loading: false,
  });
  const [usersData, setUsersData] = useState([]);
  const [usersTotalCount, setUsersTotalCount] = useState(0);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const debouncedModalSearchTerm = useDebounce(modalSearchTerm, 500);

  const columns = [
    { id: "Name", label: "SubDomain" },
    { id: "CId", label: "CID" },
    { id: "HeadTag", label: "Head Tag Preview" },
    { id: "Url", label: "Url" },
    { id: "UserCount", label: "Users" },
  ];

  const userColumns = [
    { id: "UserName", label: "UserName" },
    { id: "UserType", label: "User Type" },
    { id: "CreatedAt", label: "Assigned On" },
  ];

  useEffect(() => {
    const fetchSubDomainList = async () => {
      try {
        setDomainListLoader(true);
        const response = await axios.get("/api/Domain/GetDropDown", {
          withCredentials: true,
        });
        if (response.data.success) {
          setDomainList(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching domains:", error);
      } finally {
        setDomainListLoader(false);
      }
    };

    fetchSubDomainList();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await axios.get("/api/adminuser/GetDropdown", {
        withCredentials: true,
      });
      if (response.data.success) {
        setUsers(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const fetchAccountDetails = async () => {
    try {
      const response = await axios.get("/api/AccountDetails/GetDropDown", {
        withCredentials: true,
      });
      if (response.data.success) {
        setAccountDetails(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching account details:", error);
    }
  };

  useEffect(() => {
    if (modalState.show && users.length <= 0) {
      fetchUsers();
    }
    if (modalState.show && accountDetails.length <= 0) {
      fetchAccountDetails();
    }
  }, [modalState.show]);

  const fetchSubDomains = useCallback(async () => {
    if (!selectedOption) {
      setData([]);
      setTotalCount(0);
      return;
    }
    setIsLoading(true);

    try {
      const response = await axios.get("/api/SubDomain/Get", {
        params: {
          DomainId: selectedOption,
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy: orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });

      if (response.data.success) {
        setData(response.data.data);
        setTotalCount(response.data.pagination.recordsFiltered);
      } else {
        setData([]);
        setTotalCount(0);
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch subdomains",
        timer: 3000,
        showConfirmButton: false,
      });
      setData([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  }, [selectedOption, page, rowsPerPage, debouncedSearchTerm, order, orderBy]);

  useEffect(() => {
    fetchSubDomains();
  }, [fetchSubDomains]);

  const fetchSubDomainUsers = useCallback(async () => {
    if (!usersTableModal.subdomainId) {
      setUsersData([]);
      setUsersTotalCount(0);
      return;
    }

    try {
      setUsersTableModal((prev) => ({ ...prev, loading: true }));
      const response = await axios.get("/api/SubDomainUserMapping/Get", {
        params: {
          Id: usersTableModal.subdomainId,
          page: modalPage + 1,
          length: modalRowsPerPage,
          q: debouncedModalSearchTerm,
          orderBy: modalOrderBy,
          orderDir: modalOrder,
        },
        withCredentials: true,
      });

      if (response.data.success) {
        setUsersData(response.data.data);
        setUsersTotalCount(
          response.data.pagination?.recordsFiltered ||
            response.data.data.length,
        );
      } else {
        setUsersData([]);
        setUsersTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to fetch users",
        timer: 2000,
      });
      setUsersData([]);
      setUsersTotalCount(0);
    } finally {
      setUsersTableModal((prev) => ({ ...prev, loading: false }));
    }
  }, [
    usersTableModal.subdomainId,
    modalPage,
    modalRowsPerPage,
    debouncedModalSearchTerm,
    modalOrder,
    modalOrderBy,
  ]);

  useEffect(() => {
    if (usersTableModal.show) {
      fetchSubDomainUsers();
    }
  }, [usersTableModal.show, fetchSubDomainUsers]);

  const handleViewUsersClick = (Id, subdomainName) => {
    setUsersTableModal({
      show: true,
      subdomainId: Id,
      subdomainName,
      loading: true,
    });
    setModalSearchTerm("");
    setModalPage(0);
    setModalRowsPerPage(10);
    setModalOrder("desc");
    setModalOrderBy("");
  };

  const handleView = (rowData) => {
    if (!rowData?.Url) return;

    const url = rowData.Url.startsWith("http")
      ? rowData.Url
      : `https://${rowData.Url}`;

    window.open(url, "_blank", "noopener,noreferrer");
  };

  const handleEdit = async (rowData) => {
    try {
      setModalState((prev) => ({
        ...prev,
        isLoading: true,
        show: false,
        isEdit: true,
        data: null,
      }));

      const loadingAlert = Swal.fire({
        title: "Loading Subdomain Data",
        html: "Please wait while we fetch the details...",
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => Swal.showLoading(),
        backdrop: true,
      });

      const response = await axios.get(
        `/api/SubDomain/GetById?id=${rowData.Id}`,
      );
      const subdomainData = response.data.data[0];

      await loadingAlert.close();

      setModalState({
        show: true,
        isEdit: true,
        isLoading: false,
        data: subdomainData,
      });
    } catch (error) {
      console.error("Error fetching subdomain data:", error);
      Swal.close();

      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          error.message ||
          "Failed to load subdomain data",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#5750f1",
        allowOutsideClick: false,
      });

      setModalState((prev) => ({
        ...prev,
        show: false,
        isLoading: false,
        isEdit: false,
      }));
    }
  };

  const handleDelete = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: `You are about to delete ${rowData.Name}`,
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await axios.delete("/api/SubDomain/Delete", {
          data: { Id: rowData.Id },
          withCredentials: true,
        });
        Swal.fire("Deleted!", "SubDomain has been deleted.", "success");
        setForceRefresh((prev) => prev + 1);
        fetchSubDomains();
      } catch (error) {
        Swal.fire(
          "Error!",
          error.response?.data?.error || "Failed to delete",
          "error",
        );
      }
    }
  };

  const handleUserMappingDelete = async (rowData) => {
    setUsersTableModal({ show: false });
    const result = await Swal.fire({
      title: "Are you sure?",
      text: `You are about to delete ${rowData.UserName}`,
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await axios.delete("/api/SubDomainUserMapping/Delete", {
          data: { Id: rowData.Id },
          withCredentials: true,
        });
        Swal.fire(
          "Deleted!",
          "SubDomain User Mapping has been deleted.",
          "success",
        );
        setForceRefresh((prev) => prev + 1);
        fetchSubDomains();
      } catch (error) {
        Swal.fire(
          "Error!",
          error.response?.data?.error || "Failed to delete",
          "error",
        );
      }
    }
  };

  const handleModalSubmit = async (formData) => {
    try {
      const payload = {
        ...formData,
        Domain: selectedOption,
      };
      const url = modalState.isEdit
        ? "/api/SubDomain/Edit"
        : "/api/SubDomain/Add";
      const method = modalState.isEdit ? "put" : "post";

      const response = await axios[method](url, payload, {
        withCredentials: true,
      });

      if (response.data.success) {
        Swal.fire({
          icon: "success",
          title: "Success",
          text: `SubDomain ${modalState.isEdit ? "updated" : "added"} successfully`,
          timer: 2000,
          showConfirmButton: false,
        });
        setModalState({ show: false, isEdit: false, data: null });
        setForceRefresh((prev) => prev + 1);
        fetchSubDomains();
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error.response?.data?.error ||
          `Failed to ${modalState.isEdit ? "update" : "add"} subdomain`,
        timer: 3000,
      });
    }
  };

  const handleDomainChange = (item) => {
    setSelectedOption(item?.Id ? item?.Id : "");
    setPage(0);
  };

  const handleRequestSort = (event, property, isModal = false) => {
    if (isModal) {
      const isAsc = modalOrderBy === property && modalOrder === "asc";
      setModalOrder(isAsc ? "desc" : "asc");
      setModalOrderBy(property);
    } else {
      const isAsc = orderBy === property && order === "asc";
      setOrder(isAsc ? "desc" : "asc");
      setOrderBy(property);
    }
  };

  const handleChangePage = (newPage, isModal = false) => {
    if (isModal) {
      setModalPage(newPage);
    } else {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (newRowsPerPage, isModal = false) => {
    if (isModal) {
      setModalRowsPerPage(newRowsPerPage);
      setModalPage(0);
    } else {
      setRowsPerPage(newRowsPerPage);
      setPage(0);
    }
  };

  const handleSearchChange = (newSearchTerm, isModal = false) => {
    if (isModal) {
      setModalSearchTerm(newSearchTerm);
      setModalPage(0);
    } else {
      setSearchTerm(newSearchTerm);
      setPage(0);
    }
  };

  return (
    <>
      <div className="min-h-[600px] rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <h2 className="mb-4 text-2xl font-bold text-dark dark:text-white">
          Sub Domains
        </h2>
        <div className="flex flex-col justify-between sm:flex-row sm:items-center sm:gap-4">
          {domainListLoader ? (
            <div className="flex flex-1 justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <div className="w-full min-w-[50%] max-w-[500px] sm:w-auto">
              <SearchableDropdown
                label="Select Domain"
                options={domainList}
                placeholder="Select a Domain"
                value={selectedOption}
                onChange={handleDomainChange}
                displayKey="Name"
                idKey="Id"
              />
            </div>
          )}
          {selectedOption && (
            <Button
              type="button"
              label="Add SubDomain"
              variant="primary"
              shape="rounded"
              icon={<FaPlus size={14} />}
              onClick={() =>
                setModalState({
                  show: true,
                  isEdit: false,
                  data: null,
                })
              }
            />
          )}
        </div>
        {selectedOption && (
          <div className="mb-8">
            <CustomDataTable
              isLoading={isLoading}
              key={`${forceRefresh}-${selectedOption}`}
              columns={columns}
              rows={data}
              searchTerm={searchTerm}
              onSearchChange={(term) => handleSearchChange(term, false)}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={(newPage) => handleChangePage(newPage, false)}
              onRowsPerPageChange={(newRows) =>
                handleChangeRowsPerPage(newRows, false)
              }
              totalCount={totalCount}
              order={order}
              orderBy={orderBy}
              onRequestSort={(event, property) =>
                handleRequestSort(event, property, false)
              }
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
              handleUserCountClick={(row) =>
                handleViewUsersClick(row.Id, row.Name)
              }
            />
          </div>
        )}
      </div>

      <SubDomainModal
        isOpen={modalState.show}
        onClose={() =>
          setModalState({ show: false, isEdit: false, data: null })
        }
        onSubmit={handleModalSubmit}
        initialData={modalState.data}
        isEdit={modalState.isEdit}
        users={users}
        accountDetails={accountDetails}
        selectedDomain={selectedOption}
      />

      <Dialog
        open={usersTableModal.show}
        onClose={() =>
          setUsersTableModal({
            show: false,
            subdomainId: null,
            subdomainName: "",
          })
        }
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
            Users for {usersTableModal.subdomainName}
          </span>
          <IconButton
            aria-label="close"
            onClick={() =>
              setUsersTableModal({
                show: false,
                subdomainId: null,
                subdomainName: "",
              })
            }
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          {/* <Box sx={{ mt: 2 }}> */}
          <CustomDataTable
            key={`users-${usersTableModal.subdomainId}-${forceRefresh}`}
            isLoading={usersTableModal.loading}
            columns={userColumns}
            rows={usersData}
            searchTerm={modalSearchTerm}
            onSearchChange={(term) => handleSearchChange(term, true)}
            page={modalPage}
            rowsPerPage={modalRowsPerPage}
            onPageChange={(newPage) => handleChangePage(newPage, true)}
            onRowsPerPageChange={(newRows) =>
              handleChangeRowsPerPage(newRows, true)
            }
            totalCount={usersTotalCount}
            order={modalOrder}
            orderBy={modalOrderBy}
            onRequestSort={(event, property) =>
              handleRequestSort(event, property, true)
            }
            onDelete={handleUserMappingDelete}
          />
          {/* </Box> */}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SubDomains;
