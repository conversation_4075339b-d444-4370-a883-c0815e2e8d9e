// import { NextResponse, type NextRequest } from 'next/server';
// import { jwtVerify, SignJWT } from 'jose';

// const PUBLIC_PATHS = ['/auth/sign-in'];

// interface JWTPayload {
//   Id: string;
//   Email: string;
//   User_Type: string;
//   exp: number;
//   iat: number;
// }

// // Helper to create a new access token using `jose`
// async function createAccessToken(payload: JWTPayload): Promise<string> {
//   const secret = new TextEncoder().encode(process.env.JWT_SECRET!);

//   return await new SignJWT({
//     Id: payload.Id,
//     Email: payload.Email,
//     User_Type: payload.User_Type,
//   })
//     .setProtectedHeader({ alg: 'HS256' })
//     .setExpirationTime('15m')
//     .setIssuedAt()
//     .sign(secret);
// }

// export async function middleware(req: NextRequest) {
//   const { pathname } = req.nextUrl;

//   // Skip auth for public paths
//   if (PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
//     return NextResponse.next();
//   }

//   const accessToken = req.cookies.get('accessToken')?.value;
//   const refreshToken = req.cookies.get('refreshToken')?.value;
//   const secret = new TextEncoder().encode(process.env.JWT_SECRET!);

//   // Step 1: Try to verify accessToken
//   if (accessToken) {
//     try {
//       const { payload } = await jwtVerify(accessToken, secret) as { payload: JWTPayload };
//       const res = NextResponse.next();
//       res.headers.set('x-user-role', payload.User_Type);
//       return res;
//     } catch (error) {
//       console.warn('Access token expired or invalid:', error);
//     }
//   }

//   // Step 2: Try to verify and use refreshToken
//   if (refreshToken) {
//     try {
//       const { payload } = await jwtVerify(refreshToken, secret) as { payload: JWTPayload };

//       // Create new access token
//       const newAccessToken = await createAccessToken(payload);

//       const res = NextResponse.next();
//       res.cookies.set('accessToken', newAccessToken, {
//         httpOnly: true,
//         secure: process.env.NODE_ENV === 'production',
//         sameSite: 'lax',
//         path: '/',
//       });

//       res.headers.set('x-user-role', payload.User_Type);
//       return res;
//     } catch (error) {
//       console.error('Refresh token invalid or expired:', error);
//     }
//   }

//   // Step 3: If all fails, redirect to login
//   return NextResponse.redirect(new URL('/auth/sign-in', req.url));
// }

// // Define protected routes
// export const config = {
//   matcher: [
//     '/',
//     '/(admin|category|articles|reports|revenue|users|settings|domains|style-ids|forms)(/.*)?',
//   ],
// };


import { NextResponse, type NextRequest } from 'next/server';
import { jwtVerify, SignJWT } from 'jose';

const PUBLIC_PATHS = ['/auth/sign-in'];

interface JWTPayload {
  Id: string;
  Email: string;
  User_Type: string;
  exp: number;
  iat: number;
}

// Helper to create a new access token using `jose`
async function createAccessToken(payload: JWTPayload): Promise<string> {
  const secret = new TextEncoder().encode(process.env.JWT_SECRET!);

  return await new SignJWT({
    Id: payload.Id,
    Email: payload.Email,
    User_Type: payload.User_Type,
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('15m')
    .setIssuedAt()
    .sign(secret);
}

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Skip auth for public paths
  if (PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  const accessToken = req.cookies.get('accessToken')?.value;
  const refreshToken = req.cookies.get('refreshToken')?.value;
  const secret = new TextEncoder().encode(process.env.JWT_SECRET!);

  // Define restricted paths for non-Super Admin users
  const restrictedPaths = ['/domains', '/subdomains', '/style-ids', '/allUser'];

  // Step 1: Try to verify accessToken
  if (accessToken) {
    try {
      const { payload } = await jwtVerify(accessToken, secret) as { payload: JWTPayload };

      // Check if the user is trying to access a restricted path
      if (
        payload.User_Type !== 'Super Admin' &&
        restrictedPaths.some(path => pathname.startsWith(path))
      ) {
        console.warn(`Access denied: Non-Super Admin user attempted to access ${pathname}`);
        return NextResponse.redirect(new URL('/', req.url));
      }

      const res = NextResponse.next();
      res.headers.set('x-user-role', payload.User_Type);
      return res;
    } catch (error) {
      console.warn(`Access token expired or invalid for ${pathname}:`, error);
    }
  }

  // Step 2: Try to verify and use refreshToken
  if (refreshToken) {
    try {
      const { payload } = await jwtVerify(refreshToken, secret) as { payload: JWTPayload };

      // Check if the user is trying to access a restricted path
      if (
        payload.User_Type !== 'Super Admin' &&
        restrictedPaths.some(path => pathname.startsWith(path))
      ) {
        console.warn(`Access denied: Non-Super Admin user attempted to access ${pathname}`);
        return NextResponse.redirect(new URL('/', req.url));
      }

      // Create new access token
      const newAccessToken = await createAccessToken(payload);

      const res = NextResponse.next();
      res.cookies.set('accessToken', newAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
      });

      res.headers.set('x-user-role', payload.User_Type);
      return res;
    } catch (error) {
      console.error(`Refresh token invalid or expired for ${pathname}:`, error);
    }
  }

  // Step 3: If all fails, redirect to login
  console.warn(`Unauthenticated access attempted to ${pathname}`);
  return NextResponse.redirect(new URL('/auth/sign-in', req.url));
}

// Define protected routes
export const config = {
  matcher: [
    '/',
    '/(admin|category|articles|reports|revenue|users|settings|domains|subdomains|style-ids|forms|allUser)(/.*)?',
  ],
};